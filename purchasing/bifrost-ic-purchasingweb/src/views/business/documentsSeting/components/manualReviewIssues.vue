<template>
  <div class="manual-review-issues">
    <!-- 自定义标签页导航 -->
    <div class="custom-tabs-header">
      <div class="custom-tabs-nav">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          :class="[
            'custom-tab-button',
            `tab-${tab.key}`,
            { 'active': activeTab === tab.key }
          ]"
          @click="activeTab = tab.key"
        >
          <span class="tab-label-text">{{ tab.label }}</span>
          <span
            v-if="getTabCount(tab.key) > 0"
            class="tab-count-badge"
          >
            {{ getTabCount(tab.key) > 99 ? '99+' : getTabCount(tab.key) }}
          </span>
        </div>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="custom-tabs-content">
      <div class="tab-content">
        <div v-if="getTabItems(activeTab).length === 0" class="empty-state">
          <i class="el-icon-document-remove empty-icon"></i>
          <p class="empty-text">暂无{{ getCurrentTabLabel() }}</p>
        </div>
        <div v-else class="issues-list">
          <manual-review-item
            v-for="item in getTabItems(activeTab)"
            :key="item.id"
            :item="item"
            @locate="handleLocateOriginal"
            @edit="handleEditItem"
            @save="handleSaveItem"
            @delete="handleDeleteItem"
            @refresh-data="handleRefreshData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ManualReviewItem from './manualReviewItem.vue'

export default {
  name: 'ManualReviewIssues',
  components: {
    ManualReviewItem
  },
  props: {
    issues: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: 'all',
      tabs: [
        { key: 'all', label: '全部' },
        { key: 'unprocessed', label: '未处理' },
        { key: 'adopted', label: '已采纳' },
        { key: 'not-adopted', label: '未采纳' }
      ]
    }
  },
  computed: {
    // 全部问题
    allIssues() {
      return this.issues || [];
    },
    // 未处理的问题
    unprocessedIssues() {
      return this.allIssues.filter(item => !item.productionStatus || item.productionStatus === '未处理');
    },
    // 已采纳的问题
    adoptedIssues() {
      return this.allIssues.filter(item => item.productionStatus === '已采纳');
    },
    // 未采纳的问题
    notAdoptedIssues() {
      return this.allIssues.filter(item => item.productionStatus === '未采纳');
    }
  },
  methods: {
    getTabCount(tabKey) {
      switch (tabKey) {
        case 'all':
          return this.allIssues.length;
        case 'unprocessed':
          return this.unprocessedIssues.length;
        case 'adopted':
          return this.adoptedIssues.length;
        case 'not-adopted':
          return this.notAdoptedIssues.length;
        default:
          return 0;
      }
    },

    getTabItems(tabKey) {
      switch (tabKey) {
        case 'all':
          return this.allIssues;
        case 'unprocessed':
          return this.unprocessedIssues;
        case 'adopted':
          return this.adoptedIssues;
        case 'not-adopted':
          return this.notAdoptedIssues;
        default:
          return [];
      }
    },

    getCurrentTabLabel() {
      const currentTab = this.tabs.find(tab => tab.key === this.activeTab);
      return currentTab ? currentTab.label : '';
    },

    handleLocateOriginal(item) {
      // 向父组件传递定位原文事件
      this.$emit('locate', item);
    },

    handleEditItem(itemId) {
      // 向父组件传递编辑事件
      this.$emit('edit-item', itemId);
    },

    handleSaveItem(data) {
      // 向父组件传递保存事件
      this.$emit('save-item', data);
    },

    handleDeleteItem(data) {
      // 向父组件传递删除事件
      this.$emit('delete-item', data);
    },

    handleRefreshData() {
      // 向父组件传递数据刷新事件
      this.$emit('refresh-data');
    }
  }
}
</script>

<style scoped lang="scss">
.manual-review-issues {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// 自定义标签页头部
.custom-tabs-header {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
}

.custom-tabs-nav {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

// 自定义标签按钮基础样式
.custom-tab-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  min-height: 36px;
  box-sizing: border-box;

  .tab-count-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #f56c6c;
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    line-height: 12px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// "全部" 按钮样式 - 蓝色主题
.custom-tab-button.tab-all {
  background-color: #e8f4ff;
  color: #409eff;
  border-color: #b3d8ff;

  &.active {
    background-color: #409eff;
    color: #fff;
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }

  &:hover:not(.active) {
    background-color: #d4edff;
    border-color: #85c1ff;
  }
}

// "未处理" 按钮样式 - 灰色主题
.custom-tab-button.tab-unprocessed {
  background-color: #f4f4f5;
  color: #909399;
  border-color: #d3d4d6;

  &.active {
    background-color: #909399;
    color: #fff;
    border-color: #909399;
    box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
  }

  &:hover:not(.active) {
    background-color: #e9e9eb;
    border-color: #b4b4b6;
  }
}

// "已采纳" 按钮样式 - 绿色主题
.custom-tab-button.tab-adopted {
  background-color: #f0f9ff;
  color: #67c23a;
  border-color: #b3e19d;

  &.active {
    background-color: #67c23a;
    color: #fff;
    border-color: #67c23a;
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  }

  &:hover:not(.active) {
    background-color: #e1f3d8;
    border-color: #95d475;
  }
}

// "未采纳" 按钮样式 - 橙色主题
.custom-tab-button.tab-not-adopted {
  background-color: #fdf6ec;
  color: #e6a23c;
  border-color: #f0c78a;

  &.active {
    background-color: #e6a23c;
    color: #fff;
    border-color: #e6a23c;
    box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
  }

  &:hover:not(.active) {
    background-color: #faecd8;
    border-color: #eebe77;
  }
}

// 标签页内容区域
.custom-tabs-content {
  flex: 1;
  overflow: hidden;
  background-color: #fff;
}

.tab-content {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 20px;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 14px;
    margin: 0;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-tabs-header {
    padding: 12px;
  }

  .custom-tabs-nav {
    gap: 8px;
  }

  .custom-tab-button {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;

    .tab-count-badge {
      top: -4px;
      right: -4px;
      font-size: 9px;
      padding: 1px 4px;
      min-width: 14px;
      height: 14px;
      line-height: 12px;
    }
  }

  .tab-content {
    padding: 12px;
  }
}

// 滚动条样式
.tab-content::-webkit-scrollbar,
.issues-list::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track,
.issues-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb,
.issues-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover,
.issues-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
