<template>
  <div>
    <page ref="situationPage">
      <template #pageContent>
        <LayoutTem :isPageShow="false" :isFilterShow="false">
          <template #button>
            <div class="title-content">
              <div class="title">{{ tableTitle }}</div>
            </div>
            <div class="search-content">
              <searchForm
                ref="searchForm"
                :formOptions="formOptions"
                @onSearch="handleSearch"
                @selectChange="handleSelectChange"
                :leftviewSpan="10"
                @onReset="onReset"
              />
            </div>
          </template>
          <template #main>
            <StatisticsAll v-if="scopeInAll" :tableData="summary" />
            <StatisticsDept v-if="scopeInDept" :tableData="deptList" />
            <StatisticsDetail v-if="scopeInDetail" :tableData="detailData" />
          </template>
        </LayoutTem>
      </template>
    </page>
  </div>
</template>

<script>
import searchForm from '../reportSummer/regionSearch/searchForm.vue';
import StatisticsAll from './components/statisticsAll.vue'
import StatisticsDept from './components/statisticsDept.vue'
import StatisticsDetail from './components/statisticsDetail.vue'
export default {
  name: "AmountStatistics",
  components: {
    searchForm,
    StatisticsAll,
    StatisticsDept,
    StatisticsDetail
  },
  data() {
    return {
      formOptions: [
        {
          label: '年度',
          element: 'el-select',
          prop: 'year',
          initValue: new Date().getFullYear().toString(),
          rowSpan: 12,
          options: (() => {
            const currentYear = new Date().getFullYear();
            const options = [];
            for (let i = currentYear - 10; i <= currentYear + 10; i++) {
              const year = i.toString();
              options.push({ label: year, value: year });
            }
            return options;
          })()
        },
        {
          label: '统计范围',
          element: 'el-select',
          prop: 'queryScope',
          initValue: '按部门统计',
          rowSpan: 12,
          options: [
            { label: '按学校统计', value: '按学校统计' },
            { label: '按部门统计', value: '按部门统计' },
            { label: '明细查询', value: '明细查询' },
          ]
        }
      ],
      formQuery: {
        year: '2025',
        queryScope: '按部门统计'
      },
      allTableData: [
      ],
      tableData: [],
      deptList: [],
      summary: [],
      detailData: [],
    }
  },
  computed: {
    tableTitle() {
      const TITLE_MAP = {
        '按学校统计': '深圳大学经费卡使用情况表',
        '按部门统计': '深圳大学各院系、部门经费卡使用情况表',
        '按项目统计': '深圳大学科研项目经费使用情况表',
        '按年度统计': '深圳大学年度经费执行情况表',
        'default': '深圳大学经费卡使用情况明细表'
      }
      return TITLE_MAP[this.formQuery.queryScope] || TITLE_MAP.default
    },
    scopeInAll() {
      return this.formQuery.queryScope === '按学校统计'
    },
    scopeInDept() {
      return this.formQuery.queryScope === '按部门统计'
    },
    scopeInDetail() {
      return this.formQuery.queryScope === '明细查询'
    }
  },
  watch: {
    'formQuery': {
      handler(val) {
        if (val.queryScope === '按部门统计') {
          this.deptList = this.deptList
        } else if (val.queryScope === '明细查询') {
          this.detailData = this.detailData
        } else if (val.queryScope === '按学校统计') {
          this.summary = this.summary
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    handleSearch(obj) {
      this.formQuery.year = obj.year
      this.getData()
      this.queryExpenseCardUsedDetail()
    },
    handleSelectChange({ value, prop }) {
      // 处理下拉选择变化
      this.formQuery[prop] = value
    },
    getData() {
        this.$callApiParams(
          "queryExpenseCardUsed",
          { year: this.formQuery.year },
          (res) => {
            this.deptList = res.data.deptList
            this.summary = res.data.summary
            console.log('queryExpenseCardUsed', res.data)
            return true;
          },
          (error) => {
            reject(error);
          }
        );
    },
    queryExpenseCardUsedDetail() {
        this.$callApiParams(
          "queryExpenseCardUsedDetail",
          { year: this.formQuery.year },
          (res) => {
            this.detailData = res.data.expenseCardList
            return true;
          },
          (error) => {
            reject(error);
          }
        );
    },
    onReset(obj) {
      this.$set(this.formQuery, 'queryScope', obj.queryScope)
    }
  },
  mounted() {
    this.getDeptData()
    this.queryExpenseCardUsedDetail()
  },
}
</script>

<style lang="scss" scoped>
.title-content {
  width: 100%;
  text-align: center;
  margin: 20px 0;

  .title {
    font-size: 20px;
  }

  .description {
    margin-bottom: 20px;
    width: 100%;
    text-align: start;
    padding-top: 10px;
    font-size: 14px;
    color: #666;
  }
}
</style>
