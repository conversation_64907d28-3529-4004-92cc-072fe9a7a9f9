<template>
  <div>
    <b-curd ref="curdList"/>
    <!-- <b-list-wf-apply ref="baseListWfApply" /> -->
    <projectInfo-edit ref="projectInfoEdit" />
    <!-- <supplier-manage-dialog ref="supplierManageDialog"/> -->
    <produce-pur-file ref="producePurRef" />
    <pur-project-card-dialog ref="purProjectCardDialogRef"/>
    <bid-in-review ref="reviewRef" />
    <bid-in-prepare ref="prepareRef" />
    <review-info ref="reviewInfo"/>
    <prebid-file-dialog ref="fileDialog" />
    <projectEnd ref="projectEnd" @init="init()"/>
    <el-dialog
      title="提示"
      :visible.sync="submitVisible"
      width="620px"
      append-to-body
      class="dialog-tab-style"
      :close-on-click-modal="false"
      >
      <div style="font-weight:bold;">
        <span>是否需要采购人必须修改采购文件：</span>
        <el-radio v-model="submitResult" label="是">是</el-radio>
        <el-radio v-model="submitResult" label="否">否</el-radio>
        <div style="padding:15px 0">选择“是”，采购人不修改采购文件则不能提交审核</div>
        <div style="display: flex; align-items: center;">
          <div style="margin-right: 10px;width: 80px;align-self: flex-start;">意见备注:</div>
          <el-input type="textarea" v-model="option" :autosize="{ minRows: 8,maxRows:8}" maxlength="200" show-word-limit placeholder="请输入意见备注" />
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendAuditSubmit">确 定</el-button>
        <el-button @click="submitVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <viewDialog ref="viewDialog"></viewDialog>
  </div>
</template>

<script>
import BListWfApply from '../../../components/page/base-list-wf-apply'
import projectInfoEdit from './projectInfo-edit'
import ProducePurFile from './produce-pur-fileDialog'
import PurProjectCardDialog from '@/components/purchase/pur-project-cardDialog'
import BidInReview from "../../prebid/bid-in-review";
import BidInPrepare from "../../prebid/bid-in-prepare";
import ReviewInfo from "@/views/prebid/review-info.vue";
import PrebidFileDialog from "@/views/prebid/prebid-file-dialog.vue";
import projectEnd from "./components/project-end"
import viewDialog from "./components/viewDialog.vue"
// import supplierManageDialog from '../supplier/supplier-manage-dialog'
export default {
  name: 'projectInfo-apply1',
  // components: { BListWfAppl, projectInfoEdit},
  components: { viewDialog, projectEnd, BListWfApply,projectInfoEdit,ProducePurFile, PurProjectCardDialog,BidInReview,BidInPrepare,ReviewInfo,PrebidFileDialog},
  provide() {
    return {
      basePageInited: this.basePageInited
    };
  },
  mounted() {
      this.init()
  },
  data() {
    return {
      buttonsObj: {}, // 按钮对象
      purchaseModeOptions:[],
      procurementDocumentProducerOptions:[],
      treeDepData: [],
      submitVisible:false,
      rowId:"",
      submitResult:'是',
      isGetIsEnablePur:'',
      option:"",
      projectInfoStatus:"",
      depsTreesetting: {
        check: {
          enable: true
        },
        data: {
          simpleData: {
            enable: true,
            idKey: 'id',
            pIdKey: 'parentId'
          },
          key: {
            name: 'name'
          }
        }
      },
      showEditBidFile: true
    }
  },
  methods: {
    // 将所有的按钮放到map对象中
    async init(initParams) {
      let _this = this
      initParams = initParams || {}
      initParams.params = initParams.params || {}
      initParams.params.dataApiKey = 'pageProjectInfo'
      initParams.params.queryStatus = '制作'
      initParams.params.pageRoute = this.$getRouter()
      //	isAudit是否在本系统审核采购文件, “否”显示完成采购文件编制，“是” 不显示完成采购文件编制。显示提交采购人确认及送审
      const dataResult = await this.getIsEnablePur()
      const isAudit = await this.isAuditPurFile()
      if(dataResult=="是"){
          if(isAudit=="否"){
            initParams.hiddenButtons = ['新增招标项目','删除招标项目','修改招标项目','新增','修改','详情','打印','导出打印文件', '导出PDF','提交采购人确认及送审']
          }else{
            initParams.hiddenButtons = ['新增招标项目','删除招标项目','修改招标项目','新增','修改','详情','打印','导出打印文件', '导出PDF','完成采购文件编制']
          }
      }else{
        if(isAudit=="否"){
          initParams.hiddenButtons = ['新增','修改','详情','打印','导出打印文件', '导出PDF','提交采购人确认及送审']
        }else{
          initParams.hiddenButtons = ['新增','修改','详情','打印','导出打印文件', '导出PDF','完成采购文件编制']
        }
      }
      initParams.searchFormNum= 4,
      initParams.buttons = [
        {
          text: '新增招标项目', icon: 'el-icon-plus', enabledType: '0', click: bt=>{
            this.$refs.projectInfoEdit.show(bt,'新增')
          }
        },
        { text: '修改招标项目', icon: 'el-icon-edit', enabledType: '1', click: bt=>{
          this.$refs.projectInfoEdit.show(bt,'修改招标项目')
        }},
        { text: '编制采购文件', icon: 'el-icon-edit',  mainButton: true, enabledType: '1', click: row=>{
          //详情isedit传false
             this.$refs.producePurRef.show(row,"编制采购文件",true, this.showEditBidFile, true)
        }},
        { text: '修改采购文件', icon: 'el-icon-edit',  mainButton: true, enabledType: '1', click: row=>{
          this.$refs.producePurRef.show(row,"修改采购文件",true, this.showEditBidFile, true)
        }},
        { text: '提交采购人确认及送审', icon: 'el-icon-s-promotion', enabledType: '1', mainButton: true, click: row=>{
             this.rowId = row.getRowData().ID
             this.projectInfoStatus = row.getRowData().projectInfoStatus
             this.submitResult = "是"
             this.option =""
             this.submitVisible = true
        }},
        { text: '撤回', icon: 'el-icon-refresh-left', enabledType: '1', click: row=>{
              this.withdrawSubmit(row)
        }},
        {
            text: '审核记录', icon: 'el-icon-time', enabledType: '1', click: row => {
              this.$showWfHistory( row.getRowData().bizid, null, null, 'wfActionProjectInfoEntity' )
            }
        },
        {
            text: '采购文件详情', icon: 'el-icon-tickets', enabledType: '1', click: row => {
              this.$refs.producePurRef.show(row,"采购文件详情",false, this.showEditBidFile, true)
            }
        },
        {text: '项目卡片', icon: 'el-icon-bank-card', enabledType: '1', click: row => {
              const rows = row.getRowData()
                if(!rows.bizid && !rows.purProjectInfoBizid) {
                    this.$message.warning('暂无项目卡片信息!')
                    return false
              }
              this.$refs.purProjectCardDialogRef.show(rows.purProjectInfoBizid || rows.bizid)
              }
        },

        { text: '完成采购文件编制', icon: 'el-icon-tickets', enabledType: '1', click: row=>{
          this.$confirm('确定完成采购文件编制，进入招标准备环节？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            var projectInfo=row.getRowData();
            this.$callApiParams('finishCompile', {
              projectInfoStatus:projectInfo.projectInfoStatus,
              projectInfoBizid:projectInfo.id,
              currentNodeId:projectInfo.currentNodeId,
              currentNode:projectInfo.currentNode
            }, result => {
              this.init()
            })
            })
        }},
        {text: '编写评标信息', icon: 'el-icon-edit', enabledType: '1', click: row=>{
          this.$refs.reviewRef.show(row)
        }},
        {text: '签章评审文件、生成正式采购文件', icon: 'el-icon-coordinate', enabledType: '1',
          click: row =>{
           // this.purchaseStart(row)
            this.$refs.prepareRef.show(row)
          }
        },
        {
          text: '查看评审信息', icon: 'el-icon-tickets', enabledType: '1',
          click: bt => {
            this.$refs.reviewInfo.show(bt)
          }
        },
        {
          text: '查看正式采购文件', icon: 'el-icon-tickets', enabledType: '1',
          click: row => {
            this.prebidFile(row);
          }
        },
        {
          text: '项目终止', icon: 'el-icon-delete', enabledType: '1',
          click: row => {
            this.$refs.projectEnd.handleOpen(row)
          }
        },
        {
          text: '帮助和提示', icon: 'el-icon-magic-stick', enabledType: '1',
          click: row => {
            this.$refs.viewDialog.handleOpen('帮助和提示')
          }
        },
      ]
      initParams.btDetailClick = { click: bt => {
        this.$refs.projectInfoEdit.show(bt,'详情')
        }
      }
      initParams.btDeleteClick = {
        text: '删除招标项目', click: row => {
          this.deleteProject(row)
        }
      }
      initParams.rowRadioCallback =(scope, colItem,columnsData,trueMap,rowsData)=>{
        const optionsArr = this.$refs.curdList.getHistoryOptions().get('选择采购文件制作人下拉框')
        const labelVal = optionsArr.find(item=>item.value===scope.row.procurementDocumentProducerName).label
        this.$confirm('确定要保存分配的采购文件制作人吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$callApiParams('saveProducer', {bizId: scope.row.bizid,producerBizid:scope.row.procurementDocumentProducerName,producerName:labelVal },result => {
            if(result.success){
              this.$message({
                message: '保存成功',
                type: 'success'
              });
            }
            //this.init()
            scope.row.projectInfoStatus = "制作中"
            return true
          },res=>{
            scope.row.procurementDocumentProducerName = ""
          })
        }).catch(() => {
          scope.row.procurementDocumentProducerName = ""
        });
      }
      initParams.rowCheckedCallback= async(rows) => {
        if (this.$isNotEmpty(rows)) {
          var projectInfoStatus = rows[0].projectInfoStatus
          // 勾选请求是否开启在线制作采购文件
          await _this.getInlineProduct(rows[0]?.bizid)
          if(_this.showEditBidFile) {
            this.$call(this, 'basePage', 'setBtProperty', '编写评标信息', 'disabled', true)
          } else {
            this.$call(this, 'basePage', 'setBtProperty', '编写评标信息', 'disabled', ["审核通过","待启动采购"].includes(projectInfoStatus)? false:true)
          }
          this.$call(this, 'basePage', 'setBtProperty', '编制采购文件', 'disabled', projectInfoStatus=="制作中"||projectInfoStatus=="审核不通过" || projectInfoStatus == '采购人确认不通过' ? false:true)
          this.$call(this, 'basePage', 'setBtProperty', '提交采购人确认及送审', 'disabled', projectInfoStatus=="审核通过"||projectInfoStatus=="审核中"? true:false)
          this.$call(this, 'basePage', 'setBtProperty', '签章评审文件、生成正式采购文件', 'disabled', projectInfoStatus=="待启动采购"? false:true)
          this.$call(this, 'basePage', 'setBtProperty', '查看评审信息', 'disabled', projectInfoStatus=="审核通过"||projectInfoStatus=="待启动采购"||projectInfoStatus=="已启动采购"? false:true)
          this.$call(this, 'basePage', 'setBtProperty', '查看正式采购文件', 'disabled', projectInfoStatus=="已启动采购"? false:true)
          // todo 待完善   控制修改采购文件是否显示 不通过状态才显示 否则不显示
          if (['被退回', '采购人确认不通过'].includes(projectInfoStatus)) {
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "修改采购文件",
              "visible",
              true
            );
          }else {
            this.$call(
              this,
              "basePage",
              "setBtProperty",
              "修改采购文件",
              "visible",
              false
            );
          }
          //
        }
      }
      initParams.callbackRowDblclick= () => {}
      initParams.supTreeSelectOptions=()=>{
        this.$callApiParams('getAllUser', {},
        result => {
          let options= []
          result.data.forEach(re => {
            // this.producerData.push(re)
            options.push({
                // isChecked: re.id === row.id,
                id: re.id,
                name: re.userName,
                parentId: re.parentCode,
                code: re.userCode
              })
          })
          return options
        })
      },
      initParams.btAfters = {
        '删除招标项目': '修改招标项目',
        '撤回': '送审',
        '采购文件详情':'审核记录'
      }

      initParams.btModifyClick = {
        click: row => {
          this.showBaBizDlg(null, row.ID, false)
        }
      }

      initParams.searchForm = [
        '采购文件制作进度:PROJECT_INFO_STATUS_like:下拉:#全部:"",待分配制作人,制作中,待采购人确认,采购人确认不通过,审核中,被退回,审核通过,待启动采购,已启动采购,项目终止',
        '采购项目名称:PROJECT_INFO_NAME_like',
        // '采购部门名称:PURCHASE_DEPARTMENT_NAME_in:树:#' + JSON.stringify(this.treeDepData) + ':##' + JSON.stringify(this.depsTreesetting),
        '采购部门名称:PURCHASE_DEPARTMENT_NAME_like:文本',
        '采购标的分类:PURCHASE_CLASSIFICATION_eq:下拉:#全部:"",货物类,服务类,工程类',
        '采购方式:PROCUREMENT_METHOD_NAME_eq:下拉:#' + JSON.stringify(this.purchaseModeOptions),
        '采购文件制作人:PROCUREMENT_DOCUMENT_PRODUCER_NAME_eq:下拉:#' + JSON.stringify(this.procurementDocumentProducerOptions)
      ]

      //设置从首页跳转过来的待办
      if(this.$isNotEmpty(this.$getSessionParams("pageParameter"))){
        initParams.isShowDefaultVal = '采购文件制作进度:'+this.$getSessionParams("pageParameter")
      }
      //移除首页跳转的待办参数
      this.$removeSessionParams("pageParameter")

      this.$saveInitParams(this, initParams)
      //this.$refs.baseListWfApply.init(initParams)
      this.$refs.curdList.init(initParams)
    },
    getIsEnablePur(bizid){
      return new Promise((resolve) => {
        this.$callApiParams('getIsEnablePur', {}, result => {
          resolve(result.data)
          return true
        })
      })
    },

    //是否在本系统审核采购文件
    isAuditPurFile(){
      return new Promise((resolve) => {
        this.$callApiParams('getProjectInfoSystemParam', {
          systemKey:"IS_AUDIT_PURFILE"
        }, result => {
          resolve(result.data)
          return true
        })
      })
    },

    basePageInited(_this) {
      this.selectPurchaseMethod(_this)
      this.getDeptData(_this)
      this.selectProcurementDocumentProducer(_this)
    },
    deptCallback(_this, treeDepData) {
      _this.updateTree && _this.updateTree(['采购部门名称:PURCHASE_DEPARTMENT_NAME_in:树:#' + JSON.stringify(treeDepData) + ':##' + JSON.stringify(this.depsTreesetting)])
    },
    async getDeptData(_this) {
      this.treeDepData = await this.$getDept(_this, this.deptCallback)
    },
    selectPurchaseMethod(_this) {
      this.$callApi('selectPurchaseMethodItem', '', (result) => {
        //this.purchaseModeOptions = []
        result.data.forEach(row => {
          this.purchaseModeOptions.push({
            'value': row.name,
            'label': row.name
          })
        })
        _this.updateTree && _this.updateTree(['采购方式:PURCHASE_METHOD_NAME_eq:下拉:#' + JSON.stringify(this.purchaseModeOptions)])
        return true
      })
    },

    //选择采购文件制作人
    selectProcurementDocumentProducer(_this) {
      this.$callApiParams('refLabelValuesDynamic',
      { 'dataRef': '选择采购文件制作人下拉框'},result => {
            this.procurementDocumentProducerOptions = []
            result.data.forEach(row => {
              this.procurementDocumentProducerOptions.push({
                'value': row.label,
                'label': row.label
              })
            })
            _this.updateTree && _this.updateTree(['采购文件制作人:PURCHASE_METHOD_NAME_eq:下拉:#' + JSON.stringify(this.procurementDocumentProducerOptions)])
            return true
        })
    },
    prebidFile(row) {
      this.$refs.fileDialog.show(row)
    },
    withdrawSubmit(row){
      let params = {
        id:row.getRowData().ID
      }
      this.$confirm('确定撤回选中的项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$callApiParams('withdraw'
        , params, result => {
          this.init()
        })
      })
    },

    //删除流程
    deleteProject(row){
        let params = {
          ids:row.getRowData().ID,
          dataType:'ProjectInfoEntity',
          apiKey: 'WFDELETE'
        }
        this.$confirm('确定删除选中的项目?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$callApiParams('WFDELETE'
          , params, result => {
            this.init()
            return true
          })
        })
    },

    //提交确认
    sendAuditSubmit(){
      this.$callApiParams('submitConfirm',{id:this.rowId,projectInfoStatus:this.projectInfoStatus,submitResult:this.submitResult,option:this.option} , result => {
        this.init()
        this.submitVisible = false
      })
    },
    // 获取是否开启在线制作采购文件
    getInlineProduct(bizid='') {
      let fetchObj = bizid ?  { bizid }: { }
      return new Promise((resolve, reject) => {
        this.$callApiParams('getIsEnableOnlineFurFileSysParams',
          fetchObj,
          ({data}) => {
            const {isEnableOnlineFurFileSysParams } = data
            this.showEditBidFile =
              isEnableOnlineFurFileSysParams !== "否" || isEnableOnlineFurFileSysParams === "是" ? true : false;
            resolve();
            return true
          },
          (error) => {
            reject(error);
          }
        );
      });
    },
  }
}
</script>


